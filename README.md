# 農委會農產品交易行情API客戶端

這是一個用於存取農委會農產品交易行情API的Python客戶端程式。

## API來源
- **API文件**: https://data.moa.gov.tw/api.aspx#operations-tag-%E4%BA%A4%E6%98%93%E8%A1%8C%E6%83%85
- **API端點**: `GET /AgriProductsTransType/`

## 安裝需求

```bash
pip install -r requirements.txt
```

## 檔案說明

- `agri_products_api.py`: 主要的API客戶端類別
- `example_usage.py`: 使用範例
- `requirements.txt`: Python套件需求
- `README.md`: 說明文件

## 快速開始

### 基本使用

```python
from agri_products_api import AgriProductsAPI
from datetime import datetime, timedelta

# 建立API客戶端
api = AgriProductsAPI()

# 獲取最近7天的資料
end_date = datetime.now().strftime('%Y-%m-%d')
start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

result = api.get_transaction_data(start_date=start_date, end_date=end_date)

if 'error' not in result:
    data = result.get('Data', [])
    print(f"獲取到 {len(data)} 筆資料")
```

### 搜尋特定農產品

```python
# 搜尋高麗菜的交易資料
result = api.get_transaction_data(
    start_date="2024-01-01",
    end_date="2024-01-31",
    crop_name="高麗菜"
)
```

### 搜尋特定市場

```python
# 搜尋台北果菜市場的交易資料
result = api.get_transaction_data(
    start_date="2024-01-01",
    end_date="2024-01-31",
    market_name="台北果菜市場"
)
```

### 資料分析

```python
# 轉換為pandas DataFrame進行分析
df = api.to_dataframe(result.get('Data', []))

if not df.empty:
    print(f"平均價格: {df['Avg_Price'].mean():.2f} 元/公斤")
    print(f"農產品種類數: {df['CropName'].nunique()}")
```

### 儲存資料

```python
# 儲存為CSV檔案
api.save_to_csv(result.get('Data', []), "農產品資料.csv")
```

## API參數說明

| 參數名稱 | 說明 | 範例 |
|---------|------|------|
| start_date | 交易日期(起) | "2024-01-01" |
| end_date | 交易日期(迄) | "2024-01-31" |
| crop_code | 農產品代碼 | "A001" |
| crop_name | 農產品名稱 | "高麗菜" |
| market_name | 市場名稱 | "台北果菜市場" |
| market_code | 市場代號 | "101" |
| tc_type | 農產品種類代碼 | "1" |
| page | 頁碼控制 | "1" |

## 回應資料欄位

| 欄位名稱 | 說明 | 型態 |
|---------|------|------|
| Start_time | 交易日期(起) | string |
| End_time | 交易日期(迄) | string |
| CropCode | 農產品代碼 | string |
| CropName | 農產品名稱 | string |
| MarketName | 市場名稱 | string |
| TransDate | 交易日期 | string |
| MarketCode | 市場代號 | string |
| Upper_Price | 上價(元/公斤) | number |
| Middle_Price | 中價(元/公斤) | number |
| Lower_Price | 下價(元/公斤) | number |
| Avg_Price | 平均價(元/公斤) | number |
| Trans_Quantity | 交易量(公斤) | number |

## 執行範例

```bash
# 執行主程式範例
python agri_products_api.py

# 執行詳細使用範例
python example_usage.py
```

## 注意事項

1. API請求有頻率限制，建議在連續請求間加入適當延遲
2. 日期格式請使用 YYYY-MM-DD
3. 部分參數可能需要確切的代碼，建議先查詢可用的選項
4. 大量資料查詢時建議使用 `get_all_pages()` 方法來獲取所有頁面資料

## 錯誤處理

程式包含完整的錯誤處理機制：
- 網路請求錯誤
- JSON解析錯誤
- API回應錯誤
- 資料型態轉換錯誤

## 授權

本程式僅供學習和研究使用，請遵守農委會API的使用條款。
