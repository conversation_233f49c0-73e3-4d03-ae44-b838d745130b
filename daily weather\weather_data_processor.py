#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
農委會自動氣象站資料處理程式
功能：
1. 獲取前一天的自動氣象站資料
2. 按縣市分組計算平均值
3. 儲存為CSV檔案
"""

import requests
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import json

class WeatherDataProcessor:
    """氣象資料處理器"""

    def __init__(self):
        """初始化"""
        self.api_url = "https://data.moa.gov.tw/api/v1/AutoWeatherStationType/"
        self.output_dir = r"D:\Users\User\Desktop\n8ntest\daily data"

        # 城市名稱對應表
        self.city_mapping = {
            '南投縣': 'NTO',
            '臺中市': 'TXG',
            '台中市': 'TXG',  # API可能使用簡體字
            '臺北市': 'TPE',
            '台北市': 'TPE',  # API可能使用簡體字
            '臺南市': 'TNN',
            '台南市': 'TNN',  # API可能使用簡體字
            '臺東縣': 'TTT',
            '台東縣': 'TTT',  # API可能使用簡體字
            '嘉義市': 'CYI',
            '嘉義縣': 'CYQ',
            '基隆市': 'KEE',
            '宜蘭縣': 'ILN',
            '屏東縣': 'PIF',
            '彰化縣': 'CHA',
            '新北市': 'NTP',
            '新竹市': 'HSZ',
            '新竹縣': 'HSC',
            '桃園市': 'TAO',
            '澎湖縣': 'PEN',
            '花蓮縣': 'HUA',
            '苗栗縣': 'MIA',
            '金門縣': 'KIN',
            '雲林縣': 'YUN',
            '高雄市': 'KHH',
            '連江縣': 'LCC'
        }
    
    def get_yesterday_weather_data(self) -> List[Dict[str, Any]]:
        """獲取前一天的氣象資料"""
        # 計算前一天的日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        print(f"🌤️  正在獲取 {yesterday} 的氣象資料...")
        
        params = {
            'Start_time': yesterday,
            'End_time': yesterday
        }
        
        try:
            response = requests.get(self.api_url, params=params, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            if 'Data' in data:
                weather_data = data['Data']
                print(f"✅ 成功獲取 {len(weather_data)} 筆氣象資料")
                return weather_data
            else:
                print("❌ API回應中沒有Data欄位")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API請求失敗: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失敗: {e}")
            return []
    
    def filter_valid_stations(self, weather_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """過濾出有效的氣象站資料（有完整數據的站點）"""
        valid_data = []

        for record in weather_data:
            # 檢查是否有城市資訊和必要的氣象數據
            city = record.get('CITY', '')
            temp = record.get('TEMP')
            pres = record.get('PRES')

            # 過濾條件：有城市資訊且有基本氣象數據
            if city and temp is not None and pres is not None:
                valid_data.append(record)

        print(f"🔍 過濾後有效氣象站資料: {len(valid_data)} 筆")
        return valid_data
    
    def calculate_city_averages(self, weather_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """計算各縣市的氣象資料平均值"""
        print("📊 正在計算各縣市平均值...")

        # 轉換為DataFrame
        df = pd.DataFrame(weather_data)

        # 轉換數值欄位
        numeric_fields = ['TEMP', 'HUMD', 'PRES', 'WDSD', 'H_24R']
        for field in numeric_fields:
            df[field] = pd.to_numeric(df[field], errors='coerce')

        # 處理SUN欄位（日照時數），將空值轉為0
        df['SUN'] = pd.to_numeric(df['SUN'], errors='coerce').fillna(0)

        # 按城市分組計算平均值
        city_averages = df.groupby('CITY').agg({
            'PRES': 'mean',      # StnPres
            'TEMP': 'mean',      # Temperature
            'HUMD': 'mean',      # RH
            'WDSD': 'mean',      # WS
            'H_24R': 'mean',     # Precp
            'SUN': 'mean'        # SunShine
        }).round(2)

        # 重新命名欄位
        city_averages.columns = ['StnPres', 'Temperature', 'RH', 'WS', 'Precp', 'SunShine']

        # 重設索引，讓CITY成為一般欄位
        city_averages.reset_index(inplace=True)

        # 將城市名稱轉換為城市代號
        city_averages['city_id'] = city_averages['CITY'].map(self.city_mapping)

        # 移除沒有對應代號的城市
        city_averages = city_averages.dropna(subset=['city_id'])

        # 加入觀測時間
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y/%m/%d')
        city_averages['ObsTime'] = yesterday

        # 重新排列欄位順序
        city_averages = city_averages[['city_id', 'ObsTime', 'StnPres', 'Temperature', 'RH', 'WS', 'Precp', 'SunShine']]

        print(f"📈 完成 {len(city_averages)} 個縣市的平均值計算")

        # 顯示處理的城市
        print("處理的城市:")
        for _, row in city_averages.iterrows():
            print(f"  {row['city_id']}: 溫度 {row['Temperature']:.1f}°C, 濕度 {row['RH']:.1f}%")

        return city_averages
    
    def save_to_csv(self, data: pd.DataFrame) -> str:
        """儲存資料為CSV檔案"""
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 產生檔案名稱
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        filename = f"daily_weather_{yesterday}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"💾 資料已儲存至: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 儲存檔案失敗: {e}")
            raise
    
    def process_weather_data(self):
        """主要處理流程"""
        print("🌾 開始處理氣象資料...")
        print("=" * 50)
        
        try:
            # 1. 獲取前一天的氣象資料
            weather_data = self.get_yesterday_weather_data()
            if not weather_data:
                print("❌ 無法獲取氣象資料，程式結束")
                return
            
            # 2. 過濾有效的氣象站資料
            valid_data = self.filter_valid_stations(weather_data)
            if not valid_data:
                print("❌ 沒有有效的氣象站資料，程式結束")
                return
            
            # 3. 計算各縣市平均值
            city_averages = self.calculate_city_averages(valid_data)
            if city_averages.empty:
                print("❌ 無法計算縣市平均值，程式結束")
                return
            
            # 4. 顯示結果摘要
            print("\n📋 處理結果摘要:")
            print(f"   處理縣市數: {len(city_averages)}")
            print(f"   平均溫度範圍: {city_averages['Temperature'].min():.1f}°C - {city_averages['Temperature'].max():.1f}°C")
            print(f"   平均濕度範圍: {city_averages['RH'].min():.1f}% - {city_averages['RH'].max():.1f}%")
            
            # 5. 儲存為CSV
            filepath = self.save_to_csv(city_averages)
            
            print("\n🎉 氣象資料處理完成！")
            print("=" * 50)
            
            return filepath
            
        except Exception as e:
            print(f"❌ 處理過程發生錯誤: {e}")
            raise

def main():
    """主程式"""
    processor = WeatherDataProcessor()
    processor.process_weather_data()

if __name__ == "__main__":
    main()
