#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
農委會農產品交易行情API客戶端
API來源: https://data.moa.gov.tw/api.aspx#operations-tag-%E4%BA%A4%E6%98%93%E8%A1%8C%E6%83%85
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import time


class AgriProductsAPI:
    """農產品交易行情API客戶端"""
    
    def __init__(self):
        self.base_url = "https://data.moa.gov.tw/api/v1/AgriProductsTransType/"
        self.session = requests.Session()
        # 設定請求標頭
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def get_transaction_data(self, 
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           crop_code: Optional[str] = None,
                           crop_name: Optional[str] = None,
                           market_name: Optional[str] = None,
                           market_code: Optional[str] = None,
                           tc_type: Optional[str] = None,
                           page: Optional[str] = None) -> Dict[str, Any]:
        """
        獲取農產品交易行情資料
        
        Args:
            start_date: 交易日期(起) 格式: YYYY-MM-DD
            end_date: 交易日期(迄) 格式: YYYY-MM-DD
            crop_code: 農產品代碼
            crop_name: 農產品名稱
            market_name: 市場名稱
            market_code: 市場代號
            tc_type: 農產品種類代碼
            page: 頁碼控制
            
        Returns:
            API回應的JSON資料
        """
        
        # 建立查詢參數
        params = {}
        
        if start_date:
            params['Start_time'] = start_date
        if end_date:
            params['End_time'] = end_date
        if crop_code:
            params['CropCode'] = crop_code
        if crop_name:
            params['CropName'] = crop_name
        if market_name:
            params['MarketName'] = market_name
        if market_code:
            params['MarketCode'] = market_code
        if tc_type:
            params['TcType'] = tc_type
        if page:
            params['Page'] = page
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # 檢查回應內容
            if response.text.strip():
                return response.json()
            else:
                return {"error": "API回應為空"}
                
        except requests.exceptions.RequestException as e:
            return {"error": f"請求錯誤: {str(e)}"}
        except json.JSONDecodeError as e:
            return {"error": f"JSON解析錯誤: {str(e)}"}
    
    def get_all_pages(self, **kwargs) -> List[Dict[str, Any]]:
        """
        獲取所有頁面的資料
        
        Args:
            **kwargs: 傳遞給get_transaction_data的參數
            
        Returns:
            所有頁面的資料列表
        """
        all_data = []
        page = None
        
        while True:
            # 加入頁碼參數
            if page:
                kwargs['page'] = page
            
            result = self.get_transaction_data(**kwargs)
            
            if 'error' in result:
                print(f"錯誤: {result['error']}")
                break
            
            # 檢查是否有資料
            if 'Data' in result and result['Data']:
                all_data.extend(result['Data'])
                print(f"已獲取 {len(result['Data'])} 筆資料")
            
            # 檢查是否還有下一頁
            if result.get('Next') == True:
                page = str(int(page or '1') + 1)
                time.sleep(1)  # 避免請求過於頻繁
            else:
                break
        
        return all_data
    
    def to_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        將資料轉換為pandas DataFrame
        
        Args:
            data: API回應的資料列表
            
        Returns:
            pandas DataFrame
        """
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        
        # 轉換資料型態
        if 'TransDate' in df.columns:
            df['TransDate'] = pd.to_datetime(df['TransDate'], errors='coerce')
        
        # 轉換價格欄位為數值型態
        price_columns = ['Upper_Price', 'Middle_Price', 'Lower_Price', 'Avg_Price', 'Trans_Quantity']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def save_to_csv(self, data: List[Dict[str, Any]], filename: str):
        """
        將資料儲存為CSV檔案
        
        Args:
            data: API回應的資料列表
            filename: 檔案名稱
        """
        df = self.to_dataframe(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"資料已儲存至 {filename}")


def main():
    """主程式範例"""
    api = AgriProductsAPI()
    
    # 範例1: 獲取最近7天的資料
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    print(f"正在獲取 {start_date} 到 {end_date} 的農產品交易資料...")
    
    # 獲取單頁資料
    result = api.get_transaction_data(start_date=start_date, end_date=end_date)
    
    if 'error' not in result:
        print(f"成功獲取資料，共 {len(result.get('Data', []))} 筆")
        
        # 顯示前5筆資料
        if result.get('Data'):
            print("\n前5筆資料:")
            for i, item in enumerate(result['Data'][:5]):
                print(f"{i+1}. {item}")
        
        # 轉換為DataFrame並顯示統計資訊
        df = api.to_dataframe(result.get('Data', []))
        if not df.empty:
            print(f"\n資料統計:")
            print(f"總筆數: {len(df)}")
            print(f"農產品種類: {df['CropName'].nunique() if 'CropName' in df.columns else 'N/A'}")
            print(f"市場數量: {df['MarketName'].nunique() if 'MarketName' in df.columns else 'N/A'}")
            
            # 儲存為CSV
            filename = f"agri_products_{start_date}_to_{end_date}.csv"
            api.save_to_csv(result.get('Data', []), filename)
    else:
        print(f"獲取資料失敗: {result['error']}")
    
    # 範例2: 搜尋特定農產品
    print("\n" + "="*50)
    print("搜尋特定農產品範例 (高麗菜)")
    
    result2 = api.get_transaction_data(
        start_date=start_date,
        end_date=end_date,
        crop_name="高麗菜"
    )
    
    if 'error' not in result2 and result2.get('Data'):
        print(f"找到 {len(result2['Data'])} 筆高麗菜交易資料")
        df2 = api.to_dataframe(result2['Data'])
        if not df2.empty and 'Avg_Price' in df2.columns:
            print(f"平均價格: {df2['Avg_Price'].mean():.2f} 元/公斤")


if __name__ == "__main__":
    main()
