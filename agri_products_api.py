#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
農委會農產品交易行情API客戶端
API來源: https://data.moa.gov.tw/api.aspx#operations-tag-%E4%BA%A4%E6%98%93%E8%A1%8C%E6%83%85
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import time


class AgriProductsAPI:
    """農產品交易行情API客戶端"""
    
    def __init__(self):
        self.base_url = "https://data.moa.gov.tw/api/v1/AgriProductsTransType/"
        self.session = requests.Session()
        # 設定請求標頭
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def get_transaction_data(self,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           crop_code: Optional[str] = None,
                           crop_name: Optional[str] = None,
                           market_name: Optional[str] = None,
                           market_code: Optional[str] = None,
                           tc_type: Optional[str] = None,
                           page: Optional[str] = None) -> Dict[str, Any]:
        """
        獲取農產品交易行情資料

        Args:
            start_date: 交易日期(起) 格式: YYYY-MM-DD 或 YYY.MM.DD (民國年)
            end_date: 交易日期(迄) 格式: YYYY-MM-DD 或 YYY.MM.DD (民國年)
            crop_code: 農產品代碼
            crop_name: 農產品名稱
            market_name: 市場名稱
            market_code: 市場代號
            tc_type: 農產品種類代碼
            page: 頁碼控制

        Returns:
            API回應的JSON資料
        """

        # 建立查詢參數
        params = {}

        # 轉換日期格式為民國年格式 (YYY.MM.DD)
        if start_date:
            params['Start_time'] = self._convert_to_roc_date(start_date)
        if end_date:
            params['End_time'] = self._convert_to_roc_date(end_date)
        if crop_code:
            params['CropCode'] = crop_code
        if crop_name:
            params['CropName'] = crop_name
        if market_name:
            params['MarketName'] = market_name
        if market_code:
            params['MarketCode'] = market_code
        if tc_type:
            params['TcType'] = tc_type
        if page:
            params['Page'] = page

        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()

            # 檢查回應內容
            if response.text.strip():
                data = response.json()
                # 確保回應格式正確
                if isinstance(data, dict) and 'Data' in data:
                    return data
                else:
                    return {"error": "API回應格式不正確", "raw_data": data}
            else:
                return {"error": "API回應為空"}

        except requests.exceptions.RequestException as e:
            return {"error": f"請求錯誤: {str(e)}"}
        except json.JSONDecodeError as e:
            return {"error": f"JSON解析錯誤: {str(e)}"}

    def _convert_to_roc_date(self, date_str: str) -> str:
        """
        將西元年日期轉換為民國年格式

        Args:
            date_str: 日期字串，格式: YYYY-MM-DD

        Returns:
            民國年格式日期字串: YYY.MM.DD
        """
        try:
            if '.' in date_str:
                # 已經是民國年格式
                return date_str

            # 解析西元年日期
            from datetime import datetime
            dt = datetime.strptime(date_str, '%Y-%m-%d')

            # 轉換為民國年 (西元年 - 1911)
            roc_year = dt.year - 1911

            # 格式化為 YYY.MM.DD
            return f"{roc_year:03d}.{dt.month:02d}.{dt.day:02d}"

        except ValueError:
            # 如果轉換失敗，返回原始字串
            return date_str
    
    def get_all_pages(self, **kwargs) -> List[Dict[str, Any]]:
        """
        獲取所有頁面的資料

        Args:
            **kwargs: 傳遞給get_transaction_data的參數

        Returns:
            所有頁面的資料列表
        """
        all_data = []
        page = "1"  # 從第1頁開始

        while True:
            # 加入頁碼參數
            kwargs['page'] = page

            result = self.get_transaction_data(**kwargs)

            if 'error' in result:
                print(f"錯誤: {result['error']}")
                break

            # 檢查是否有資料
            if 'Data' in result and result['Data']:
                all_data.extend(result['Data'])
                print(f"第{page}頁: 獲取 {len(result['Data'])} 筆資料，累計 {len(all_data)} 筆")
            else:
                print(f"第{page}頁: 沒有資料")
                break

            # 檢查是否還有下一頁
            if result.get('Next') == True:
                page = str(int(page) + 1)
                time.sleep(1)  # 避免請求過於頻繁
            else:
                print("已獲取所有頁面資料")
                break

        return all_data
    
    def to_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        將資料轉換為pandas DataFrame

        Args:
            data: API回應的資料列表

        Returns:
            pandas DataFrame
        """
        if not data:
            return pd.DataFrame()

        df = pd.DataFrame(data)

        # 轉換民國年日期為西元年
        if 'TransDate' in df.columns:
            df['TransDate'] = df['TransDate'].apply(self._convert_roc_to_ad_date)
            df['TransDate'] = pd.to_datetime(df['TransDate'], errors='coerce')

        # 轉換價格欄位為數值型態
        price_columns = ['Upper_Price', 'Middle_Price', 'Lower_Price', 'Avg_Price', 'Trans_Quantity']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        return df

    def _convert_roc_to_ad_date(self, roc_date_str: str) -> str:
        """
        將民國年日期轉換為西元年格式

        Args:
            roc_date_str: 民國年日期字串，格式: YYY.MM.DD

        Returns:
            西元年格式日期字串: YYYY-MM-DD
        """
        try:
            if not roc_date_str or roc_date_str == '-':
                return None

            # 解析民國年日期
            parts = roc_date_str.split('.')
            if len(parts) == 3:
                roc_year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])

                # 轉換為西元年 (民國年 + 1911)
                ad_year = roc_year + 1911

                return f"{ad_year:04d}-{month:02d}-{day:02d}"
            else:
                return roc_date_str

        except (ValueError, AttributeError):
            return roc_date_str
    
    def save_to_csv(self, data: List[Dict[str, Any]], filename: str):
        """
        將資料儲存為CSV檔案
        
        Args:
            data: API回應的資料列表
            filename: 檔案名稱
        """
        df = self.to_dataframe(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"資料已儲存至 {filename}")


def main():
    """主程式範例"""
    api = AgriProductsAPI()

    print("=== 農委會農產品交易行情API測試 ===")

    # 範例1: 獲取最新資料 (不指定日期範圍)
    print("\n1. 獲取最新農產品交易資料...")

    result = api.get_transaction_data()

    if 'error' not in result:
        data_count = len(result.get('Data', []))
        print(f"成功獲取資料，共 {data_count} 筆")

        # 顯示前3筆資料
        if result.get('Data'):
            print("\n前3筆資料:")
            for i, item in enumerate(result['Data'][:3]):
                print(f"{i+1}. 日期: {item.get('TransDate', 'N/A')}, "
                      f"農產品: {item.get('CropName', 'N/A')}, "
                      f"市場: {item.get('MarketName', 'N/A')}, "
                      f"平均價: {item.get('Avg_Price', 0)} 元/公斤")

        # 轉換為DataFrame並顯示統計資訊
        df = api.to_dataframe(result.get('Data', []))
        if not df.empty:
            print(f"\n資料統計:")
            print(f"總筆數: {len(df)}")

            # 過濾掉休市資料
            active_df = df[df['CropName'] != '休市']
            if not active_df.empty:
                print(f"有效交易筆數: {len(active_df)}")
                print(f"農產品種類: {active_df['CropName'].nunique()}")
                print(f"市場數量: {active_df['MarketName'].nunique()}")

                # 顯示有價格的資料統計
                price_df = active_df[active_df['Avg_Price'] > 0]
                if not price_df.empty:
                    print(f"有價格資料筆數: {len(price_df)}")
                    print(f"平均價格範圍: {price_df['Avg_Price'].min():.2f} - {price_df['Avg_Price'].max():.2f} 元/公斤")

            # 儲存為CSV
            filename = f"最新農產品資料_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            api.save_to_csv(result.get('Data', []), filename)
    else:
        print(f"獲取資料失敗: {result['error']}")

    # 範例2: 搜尋特定農產品
    print("\n" + "="*50)
    print("2. 搜尋特定農產品範例")

    # 嘗試搜尋幾種常見農產品
    crops_to_search = ["高麗菜", "白蘿蔔", "胡蘿蔔", "青江菜", "小白菜"]

    for crop_name in crops_to_search:
        print(f"\n搜尋 {crop_name}...")
        result2 = api.get_transaction_data(crop_name=crop_name)

        if 'error' not in result2 and result2.get('Data'):
            data = result2['Data']
            # 過濾有效資料
            valid_data = [item for item in data if item.get('CropName') == crop_name and item.get('Avg_Price', 0) > 0]

            if valid_data:
                print(f"找到 {len(valid_data)} 筆 {crop_name} 的有效交易資料")
                df2 = api.to_dataframe(valid_data)
                if not df2.empty and 'Avg_Price' in df2.columns:
                    avg_price = df2['Avg_Price'].mean()
                    min_price = df2['Avg_Price'].min()
                    max_price = df2['Avg_Price'].max()
                    print(f"  平均價格: {avg_price:.2f} 元/公斤")
                    print(f"  價格範圍: {min_price:.2f} - {max_price:.2f} 元/公斤")

                    # 顯示市場分布
                    if 'MarketName' in df2.columns:
                        markets = df2['MarketName'].unique()
                        print(f"  交易市場: {', '.join(markets[:3])}{'...' if len(markets) > 3 else ''}")
                break
            else:
                print(f"沒有找到 {crop_name} 的有效交易資料")
        else:
            print(f"搜尋 {crop_name} 失敗")

    print(f"\n=== 測試完成 ===")
    print("如需獲取更多資料，請參考 example_usage.py 中的詳細範例")


if __name__ == "__main__":
    main()
