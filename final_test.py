#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
農委會農產品交易行情API - 完整功能測試
"""

from agri_products_api import AgriProductsAPI
from datetime import datetime, timedelta

def main():
    """完整功能測試"""
    print("🌾 農委會農產品交易行情API - 完整功能測試 🌾")
    print("=" * 60)
    
    api = AgriProductsAPI()
    
    # 測試1: 基本資料獲取
    print("\n📊 測試1: 獲取最新農產品交易資料")
    print("-" * 40)
    
    result = api.get_transaction_data()
    if 'error' not in result:
        data = result.get('Data', [])
        valid_data = [item for item in data if item.get('CropName') != '休市' and item.get('Avg_Price', 0) > 0]
        
        print(f"✅ 成功獲取 {len(data)} 筆資料")
        print(f"📈 有效交易資料: {len(valid_data)} 筆")
        
        if valid_data:
            df = api.to_dataframe(valid_data)
            print(f"🥬 農產品種類: {df['CropName'].nunique()}")
            print(f"🏪 交易市場: {df['MarketName'].nunique()}")
            print(f"💰 價格範圍: {df['Avg_Price'].min():.2f} - {df['Avg_Price'].max():.2f} 元/公斤")
    else:
        print(f"❌ 錯誤: {result['error']}")
    
    # 測試2: 農產品搜尋
    print("\n🔍 測試2: 搜尋特定農產品")
    print("-" * 40)
    
    test_crops = ["小番茄-聖女", "蘿蔔-青色", "香蕉-其他"]
    
    for crop_name in test_crops:
        result = api.get_transaction_data(crop_name=crop_name)
        if 'error' not in result:
            data = result.get('Data', [])
            valid_data = [item for item in data if item.get('CropName') == crop_name and item.get('Avg_Price', 0) > 0]
            
            if valid_data:
                df = api.to_dataframe(valid_data)
                avg_price = df['Avg_Price'].mean()
                market_count = df['MarketName'].nunique()
                print(f"🥕 {crop_name}: {len(valid_data)} 筆交易, 平均 {avg_price:.2f} 元/公斤, {market_count} 個市場")
            else:
                print(f"⚠️  {crop_name}: 無有效交易資料")
        else:
            print(f"❌ {crop_name}: 搜尋失敗")
    
    # 測試3: 市場搜尋
    print("\n🏪 測試3: 搜尋特定市場")
    print("-" * 40)
    
    test_markets = ["台北一", "台中市", "高雄市"]
    
    for market_name in test_markets:
        result = api.get_transaction_data(market_name=market_name)
        if 'error' not in result:
            data = result.get('Data', [])
            valid_data = [item for item in data if item.get('MarketName') == market_name and item.get('CropName') != '休市']
            
            if valid_data:
                df = api.to_dataframe(valid_data)
                crop_count = df['CropName'].nunique()
                print(f"🏪 {market_name}: {len(valid_data)} 筆交易, {crop_count} 種農產品")
            else:
                print(f"⚠️  {market_name}: 無有效交易資料")
        else:
            print(f"❌ {market_name}: 搜尋失敗")
    
    # 測試4: 日期範圍搜尋
    print("\n📅 測試4: 日期範圍搜尋")
    print("-" * 40)
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    print(f"搜尋日期: {start_date} 到 {end_date}")
    
    result = api.get_transaction_data(start_date=start_date, end_date=end_date)
    if 'error' not in result:
        data = result.get('Data', [])
        valid_data = [item for item in data if item.get('CropName') != '休市' and item.get('Avg_Price', 0) > 0]
        
        print(f"✅ 找到 {len(valid_data)} 筆有效交易資料")
        
        if valid_data:
            df = api.to_dataframe(valid_data)
            
            # 最貴的3種農產品
            top_prices = df.groupby('CropName')['Avg_Price'].mean().sort_values(ascending=False).head(3)
            print("\n💎 最貴的3種農產品:")
            for i, (crop, price) in enumerate(top_prices.items()):
                print(f"  {i+1}. {crop}: {price:.2f} 元/公斤")
            
            # 最便宜的3種農產品
            low_prices = df.groupby('CropName')['Avg_Price'].mean().sort_values().head(3)
            print("\n💰 最便宜的3種農產品:")
            for i, (crop, price) in enumerate(low_prices.items()):
                print(f"  {i+1}. {crop}: {price:.2f} 元/公斤")
    else:
        print(f"❌ 錯誤: {result['error']}")
    
    # 測試5: 資料匯出
    print("\n💾 測試5: 資料匯出")
    print("-" * 40)
    
    if 'error' not in result and result.get('Data'):
        filename = f"測試匯出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        api.save_to_csv(result['Data'], filename)
        print(f"✅ 資料已匯出至: {filename}")
    
    print("\n🎉 所有測試完成！")
    print("=" * 60)
    print("📝 使用說明:")
    print("1. 可以搜尋特定農產品名稱")
    print("2. 可以搜尋特定市場")
    print("3. 可以指定日期範圍")
    print("4. 可以組合多個搜尋條件")
    print("5. 資料會自動轉換為pandas DataFrame方便分析")
    print("6. 支援匯出CSV檔案")

if __name__ == "__main__":
    main()
