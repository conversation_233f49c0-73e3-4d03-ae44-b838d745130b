#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試農委會API
"""

import requests
import json

def test_api():
    """測試API端點"""
    url = 'https://data.moa.gov.tw/api/v1/AgriProductsTransType/'
    
    try:
        response = requests.get(url, timeout=30)
        print(f'狀態碼: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'資料結構: {type(data)}')
            
            if isinstance(data, dict):
                print(f'字典鍵值: {list(data.keys())}')
                if 'Data' in data:
                    data_len = len(data['Data'])
                    print(f'Data 長度: {data_len}')
                    if data['Data']:
                        first_item = data['Data'][0]
                        print('第一筆資料:')
                        print(json.dumps(first_item, ensure_ascii=False, indent=2))
                        
                        # 檢查是否有Next欄位
                        if 'Next' in data:
                            print(f'Next: {data["Next"]}')
                            
            elif isinstance(data, list):
                print(f'列表長度: {len(data)}')
                if data:
                    first_item = data[0]
                    print('第一筆資料:')
                    print(json.dumps(first_item, ensure_ascii=False, indent=2))
        else:
            print(f'錯誤: {response.status_code}')
            print(f'回應內容: {response.text}')
            
    except Exception as e:
        print(f'發生錯誤: {e}')

if __name__ == "__main__":
    test_api()
