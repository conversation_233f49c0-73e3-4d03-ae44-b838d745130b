#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試日期範圍搜尋功能
"""

from agri_products_api import AgriProductsAPI
from datetime import datetime, timedelta

def test_date_range():
    api = AgriProductsAPI()

    # 測試日期範圍搜尋
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    print(f'搜尋日期範圍: {start_date} 到 {end_date}')

    result = api.get_transaction_data(start_date=start_date, end_date=end_date)

    if 'error' not in result and result.get('Data'):
        data = result['Data']
        print(f'找到 {len(data)} 筆交易資料')
        
        # 過濾有效資料
        valid_data = [item for item in data if item.get('CropName') != '休市' and item.get('Avg_Price', 0) > 0]
        
        if valid_data:
            print(f'有效交易資料: {len(valid_data)} 筆')
            
            df = api.to_dataframe(valid_data)
            if not df.empty:
                print(f'農產品種類: {df["CropName"].nunique()}')
                print(f'交易市場: {df["MarketName"].nunique()}')
                print(f'平均價格範圍: {df["Avg_Price"].min():.2f} - {df["Avg_Price"].max():.2f} 元/公斤')
                
                # 顯示最貴的5種農產品
                print('\n最貴的5種農產品:')
                top_prices = df.groupby('CropName')['Avg_Price'].mean().sort_values(ascending=False).head(5)
                for i, (crop, price) in enumerate(top_prices.items()):
                    print(f'{i+1}. {crop}: {price:.2f} 元/公斤')
                
                # 顯示最便宜的5種農產品
                print('\n最便宜的5種農產品:')
                low_prices = df.groupby('CropName')['Avg_Price'].mean().sort_values().head(5)
                for i, (crop, price) in enumerate(low_prices.items()):
                    print(f'{i+1}. {crop}: {price:.2f} 元/公斤')
        else:
            print('沒有找到有效的交易資料')
    else:
        print(f'搜尋失敗: {result.get("error", "未知錯誤")}')

if __name__ == "__main__":
    test_date_range()
