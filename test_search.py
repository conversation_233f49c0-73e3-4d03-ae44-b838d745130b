#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試農產品搜尋功能
"""

from agri_products_api import AgriProductsAPI

def test_search():
    api = AgriProductsAPI()

    # 測試搜尋實際存在的農產品
    crop_name = '小番茄-聖女'
    print(f'搜尋農產品: {crop_name}')

    result = api.get_transaction_data(crop_name=crop_name)

    if 'error' not in result and result.get('Data'):
        data = result['Data']
        valid_data = [item for item in data if item.get('CropName') == crop_name and item.get('Avg_Price', 0) > 0]
        
        if valid_data:
            print(f'找到 {len(valid_data)} 筆 {crop_name} 的有效交易資料')
            
            df = api.to_dataframe(valid_data)
            if not df.empty:
                avg_price = df['Avg_Price'].mean()
                min_price = df['Avg_Price'].min()
                max_price = df['Avg_Price'].max()
                
                print(f'平均價格: {avg_price:.2f} 元/公斤')
                print(f'價格範圍: {min_price:.2f} - {max_price:.2f} 元/公斤')
                print(f'交易市場: {list(df["MarketName"].unique())}')
                
                # 顯示前3筆詳細資料
                print('\n前3筆交易資料:')
                for i, (idx, row) in enumerate(df.head(3).iterrows()):
                    print(f'{i+1}. 日期: {row["TransDate"]}, 市場: {row["MarketName"]}, 平均價: {row["Avg_Price"]} 元/公斤')
        else:
            print(f'沒有找到 {crop_name} 的有效交易資料')
    else:
        print(f'搜尋失敗: {result.get("error", "未知錯誤")}')

if __name__ == "__main__":
    test_search()
