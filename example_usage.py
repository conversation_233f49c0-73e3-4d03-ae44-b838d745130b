#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
農產品交易行情API使用範例
"""

from agri_products_api import AgriProductsAPI
from datetime import datetime, timedelta


def example_basic_usage():
    """基本使用範例"""
    print("=== 基本使用範例 ===")
    
    api = AgriProductsAPI()
    
    # 獲取最近3天的資料
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    result = api.get_transaction_data(start_date=start_date, end_date=end_date)
    
    if 'error' not in result:
        data = result.get('Data', [])
        print(f"獲取到 {len(data)} 筆資料")
        
        if data:
            print("第一筆資料:")
            for key, value in data[0].items():
                print(f"  {key}: {value}")
    else:
        print(f"錯誤: {result['error']}")


def example_search_by_crop():
    """依農產品名稱搜尋範例"""
    print("\n=== 依農產品名稱搜尋範例 ===")

    api = AgriProductsAPI()

    # 搜尋特定農產品 (例如: 小番茄-聖女)
    crop_name = "小番茄-聖女"

    result = api.get_transaction_data(crop_name=crop_name)

    if 'error' not in result:
        data = result.get('Data', [])
        # 過濾有效資料
        valid_data = [item for item in data if item.get('CropName') == crop_name and item.get('Avg_Price', 0) > 0]

        print(f"找到 {len(valid_data)} 筆 {crop_name} 的有效交易資料")

        if valid_data:
            # 轉換為DataFrame進行分析
            df = api.to_dataframe(valid_data)
            if not df.empty and 'Avg_Price' in df.columns:
                print(f"平均價格: {df['Avg_Price'].mean():.2f} 元/公斤")
                print(f"最高價格: {df['Avg_Price'].max():.2f} 元/公斤")
                print(f"最低價格: {df['Avg_Price'].min():.2f} 元/公斤")
                print(f"交易市場: {', '.join(df['MarketName'].unique()[:5])}")

                # 顯示最新的3筆交易
                print("\n最新3筆交易:")
                for i, (idx, row) in enumerate(df.head(3).iterrows()):
                    print(f"  {i+1}. {row['TransDate'].strftime('%Y-%m-%d')} {row['MarketName']} {row['Avg_Price']} 元/公斤")
    else:
        print(f"錯誤: {result['error']}")


def example_search_by_market():
    """依市場名稱搜尋範例"""
    print("\n=== 依市場名稱搜尋範例 ===")

    api = AgriProductsAPI()

    # 搜尋特定市場 (例如: 台北一)
    market_name = "台北一"

    result = api.get_transaction_data(market_name=market_name)

    if 'error' not in result:
        data = result.get('Data', [])
        # 過濾有效資料
        valid_data = [item for item in data if item.get('MarketName') == market_name and item.get('CropName') != '休市']

        print(f"找到 {len(valid_data)} 筆 {market_name} 的有效交易資料")

        if valid_data:
            # 分析不同農產品
            df = api.to_dataframe(valid_data)
            if not df.empty and 'CropName' in df.columns:
                crop_counts = df['CropName'].value_counts()
                print(f"該市場交易的農產品種類數: {len(crop_counts)}")
                print("前5名交易筆數最多的農產品:")
                for i, (crop, count) in enumerate(crop_counts.head().items()):
                    print(f"  {i+1}. {crop}: {count} 筆交易")

                # 顯示價格最高的農產品
                if 'Avg_Price' in df.columns:
                    price_df = df[df['Avg_Price'] > 0]
                    if not price_df.empty:
                        top_prices = price_df.groupby('CropName')['Avg_Price'].mean().sort_values(ascending=False).head(5)
                        print("\n價格最高的5種農產品:")
                        for i, (crop, price) in enumerate(top_prices.items()):
                            print(f"  {i+1}. {crop}: {price:.2f} 元/公斤")
    else:
        print(f"錯誤: {result['error']}")


def example_save_to_csv():
    """儲存資料為CSV範例"""
    print("\n=== 儲存資料為CSV範例 ===")
    
    api = AgriProductsAPI()
    
    # 獲取資料
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    result = api.get_transaction_data(start_date=start_date, end_date=end_date)
    
    if 'error' not in result:
        data = result.get('Data', [])
        if data:
            filename = f"農產品交易資料_{start_date}_to_{end_date}.csv"
            api.save_to_csv(data, filename)
            print(f"資料已儲存至 {filename}")
        else:
            print("沒有資料可儲存")
    else:
        print(f"錯誤: {result['error']}")


def example_get_all_pages():
    """獲取所有頁面資料範例"""
    print("\n=== 獲取所有頁面資料範例 ===")
    
    api = AgriProductsAPI()
    
    # 獲取特定農產品的所有資料
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    print(f"正在獲取 {start_date} 到 {end_date} 的所有資料...")
    
    all_data = api.get_all_pages(
        start_date=start_date,
        end_date=end_date
    )
    
    if all_data:
        print(f"總共獲取 {len(all_data)} 筆資料")
        
        # 分析資料
        df = api.to_dataframe(all_data)
        if not df.empty:
            print(f"農產品種類數: {df['CropName'].nunique() if 'CropName' in df.columns else 'N/A'}")
            print(f"市場數量: {df['MarketName'].nunique() if 'MarketName' in df.columns else 'N/A'}")
            
            # 儲存完整資料
            filename = f"完整農產品資料_{start_date}_to_{end_date}.csv"
            api.save_to_csv(all_data, filename)
    else:
        print("沒有獲取到資料")


if __name__ == "__main__":
    # 執行所有範例
    example_basic_usage()
    example_search_by_crop()
    example_search_by_market()
    example_save_to_csv()
    example_get_all_pages()
