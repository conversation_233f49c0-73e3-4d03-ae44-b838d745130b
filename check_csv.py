import pandas as pd

df = pd.read_csv('daily data/weather_data_20250729.csv')
print('更新後的CSV檔案內容:')
print(f'總筆數: {len(df)}')
print(f'欄位: {list(df.columns)}')
print('\n前5筆資料:')
print(df.head().to_string(index=False))
print('\n檢查SunShine欄位:')
print(f'SunShine最小值: {df["SunShine"].min()}')
print(f'SunShine最大值: {df["SunShine"].max()}')
print(f'SunShine平均值: {df["SunShine"].mean():.2f}')
print(f'SunShine是否有NaN: {df["SunShine"].isna().any()}')

print('\n完整資料摘要:')
for _, row in df.iterrows():
    print(f'{row["city_id"]}: 溫度{row["Temperature"]:.1f}°C, 濕度{row["RH"]:.1f}%, 氣壓{row["StnPres"]:.1f}hPa, 風速{row["WS"]:.1f}m/s, 雨量{row["Precp"]:.1f}mm, 日照{row["SunShine"]:.1f}h')
